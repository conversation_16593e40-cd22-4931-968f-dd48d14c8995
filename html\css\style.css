@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@300&display=swap');

.drawtext {
    display: flex;
    position: absolute;

    opacity: 0;
	left: 0%;
    bottom: 5%;

    width: 14%;
    height: 6vh;
    border-radius: 2px;
    background-color: rgba(26, 27, 30, 0.812);
    filter: drop-shadow(0px 0px 5px #000000);

    font-family: '<PERSON>', sans-serif;
    font-weight: 200;
    user-select: none;
}

.drawtext > .keys {
    display: flex;
    padding-left: 5px;
    height: 100%;
}

.drawtext > .keys > .key {
    width: 2.5vw;
    height: 75%;
    font-weight: bold;

    border-radius: 15%;

    background-color: rgba(26, 27, 30, 0.712);

    position: relative;
    top: 50%;
    transform: translate(0%, -50%);
    margin-right: 5px;
}

.drawtext > .keys > .plus {
    margin-top: 1.4vh;
    font-weight: bolder;

    margin-right: 5px;
    font-size: 2.1vh;
    color: white;
    height: 100%;
}

.drawtext > .keys > .key::after {
    content: '';
    display: block;

    width: 80%;
    height: 80%;
    border-radius: 7px;

    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    border: 2px solid white;
}

.drawtext > .keys > .key span {

    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 15px;
}

.drawtext > .text {
    line-height: 0px;
    height: 90%;
    position: relative;
}

.drawtext > .text::before {
    content: '';
    display: block;
    height: 5vh;
    width: 3px;
    background-color: white;

    position: absolute;
    top: 55%;
    transform: translate(0%, -50%);
}

.drawtext > .text > .title {
    color: #1c7ed6;
    text-shadow: 0px 0px 5px rgb(28, 126, 214, 0.1);
    margin-left: 10px;
}

.drawtext > .text > .desc {
    color: white;
    margin-left: 10px;
}

.second,
.plus {
    display: none;
}