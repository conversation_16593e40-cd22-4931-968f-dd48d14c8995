@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@300;400;500&display=swap');

.drawtext {
    display: flex;
    position: absolute;
    align-items: center;

    opacity: 0;
	left: 0%;
    bottom: 5%;

    width: 18%;
    height: 6vh;
    border-radius: 4px;
    background-color: rgba(26, 27, 30, 0.9);
    filter: drop-shadow(0px 0px 8px rgba(0, 0, 0, 0.5));

    font-family: '<PERSON>', sans-serif;
    font-weight: 400;
    user-select: none;
    padding: 8px 12px;
    box-sizing: border-box;
}

.drawtext > .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    margin-right: 12px;
    flex-shrink: 0;
}

.drawtext > .icon::before {
    content: '⏱';
    font-size: 20px;
    color: white;
}

.drawtext > .separator {
    width: 3px;
    height: 40px;
    background-color: #00bcd4;
    margin-right: 12px;
    flex-shrink: 0;
}

.drawtext > .text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    line-height: 1.2;
}

.drawtext > .text > .title {
    color: #00bcd4;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 2px 0;
    text-shadow: 0px 0px 3px rgba(0, 188, 212, 0.3);
}

.drawtext > .text > .desc {
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    font-weight: 300;
    margin: 0;
}

/* Hide unused elements */
.keys {
    display: none;
}